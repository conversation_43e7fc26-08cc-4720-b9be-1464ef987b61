# Contact Us Architecture Fix

This document explains the fix for the database architecture issue in the contact us system.

## Problem Analysis

### Original Issue
- **Error**: `django.db.utils.OperationalError: no such table: inquiry_inquiry`
- **Root Cause**: Frontend was trying to save contact form data to its own database
- **Architecture Problem**: In a frontend-backend separated project, data should only be stored in the backend

### Why This Happened
1. **Mixed Architecture**: Frontend was using Django ORM to save data locally
2. **Missing Backend API**: No backend endpoint for contact inquiries
3. **Database Separation**: Frontend and backend should have separate responsibilities

## Solution Implemented

### 1. Backend Changes

#### Added Inquiry Model (`backend/api/models.py`)
```python
class Inquiry(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.Char<PERSON>ield(max_length=200, blank=True, null=True)
    email = models.EmailField()
    inquiry_type = models.PositiveSmallIntegerField(choices=INQUIRY_TYPE_CHOICES, default=0)
    description = models.TextField(max_length=1500)
    created_at = models.DateTimeField(auto_now_add=True)
```

#### Added API Endpoint (`backend/api/views.py`)
```python
class ContactInquiryView(APIView):
    permission_classes = [AllowAny]  # Allow anonymous users
    
    def post(self, request):
        # Validate and save inquiry to backend database
        # Return JSON response
```

#### Added URL Route (`backend/api/urls.py`)
```python
path('contact/inquiry/', views.ContactInquiryView.as_view(), name='contact_inquiry'),
```

### 2. Frontend Changes

#### Modified Frontend API View (`app_frontend/inquiry/views.py`)
- **Before**: Used Django ORM to save to frontend database
- **After**: Makes HTTP request to backend API

```python
def inquiry_api_view(request):
    # Forward request to backend API
    backend_url = f"{API_BASE_URL}/api/contact/inquiry/"
    response = requests.post(backend_url, data=data)
    return JsonResponse(response.json())
```

### 3. Database Migration

#### Backend Migration Required
```bash
cd backend
python manage.py migrate
```

#### Frontend Migration NOT Required
- Frontend no longer uses local database for inquiries
- Existing frontend inquiry tables can be ignored/removed

## New Architecture Flow

### Contact Form Submission
1. **User** fills out contact form on frontend
2. **Frontend JavaScript** submits form to `/contact-us/api/`
3. **Frontend View** forwards request to backend API
4. **Backend API** validates and saves to backend database
5. **Backend** returns success/error response
6. **Frontend** displays result to user

### Message Management
1. **Admin** accesses message management page
2. **Frontend** calls backend API to fetch inquiries
3. **Backend** returns inquiry data with pagination
4. **Frontend** displays messages in admin interface

## API Endpoints

### Backend API
- **POST** `/api/contact/inquiry/` - Submit new inquiry
- **GET** `/api/contact/inquiries/` - List inquiries (admin only, to be implemented)

### Frontend API (Proxy)
- **POST** `/contact-us/api/` - Forwards to backend API

## Data Flow Diagram

```
User Form → Frontend View → Backend API → Backend Database
     ↑                                           ↓
User Interface ← Frontend View ← Backend Response
```

## Benefits of New Architecture

1. **Proper Separation**: Frontend handles UI, backend handles data
2. **Single Source of Truth**: All data stored in backend only
3. **Scalability**: Backend can serve multiple frontends
4. **Security**: Centralized data validation and storage
5. **Consistency**: All APIs follow same pattern

## Migration Steps for You

### 1. Run Backend Migration
```bash
cd backend
python manage.py migrate
```

### 2. Test Contact Form
1. Visit contact us page
2. Submit a form (without login)
3. Should work without database errors

### 3. Verify Data Storage
- Check backend database for new inquiry records
- Frontend database should not be used for inquiries

## Troubleshooting

### If Contact Form Still Fails
1. Check backend server is running on port 8001
2. Verify backend API endpoint is accessible
3. Check network requests in browser DevTools
4. Look for CORS errors in console

### If Backend Migration Fails
1. Check backend database connection
2. Ensure all dependencies are installed
3. Run `python manage.py makemigrations api` first if needed

## Future Improvements

1. **Admin Message Management**: Implement backend API for listing inquiries
2. **Email Notifications**: Send emails when inquiries are received
3. **Response System**: Allow admins to respond to inquiries
4. **Analytics**: Track inquiry types and response times

This fix ensures proper frontend-backend separation and eliminates the database table error.
