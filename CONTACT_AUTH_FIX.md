# Contact Us Authentication Fix

This document explains the fix for the authentication detection issue in the Contact Us page.

## Problem
The Contact Us page was still showing the login prompt even after successful login because:
1. Frontend and backend are separated
2. Authentication is JWT-based, not Django session-based
3. The template was using Django's `request.user` which doesn't work with JWT tokens
4. User authentication status needs to be checked via API calls

## Solution
Implemented client-side authentication detection using JavaScript and API calls.

## Changes Made

### 1. Backend View (`app_frontend/inquiry/views.py`)
- **Removed**: Server-side user detection (`request.user`)
- **Result**: Template no longer depends on Django session authentication

### 2. Frontend Template (`app_frontend/templates/html/inquiry/inquiry-create.html`)

#### HTML Changes:
- **Login Prompt**: Now controlled by JavaScript (`display: none` by default)
- **User Status**: Dynamic display of logged-in user email
- **Form Fields**: No longer pre-filled by Django template, handled by JavaScript
- **Auto-filled Labels**: Controlled by JavaScript

#### JavaScript Changes:
- **`isAuthenticated()`**: Checks for valid JWT token in localStorage
- **`getUserProfile()`**: Fetches user data from `/api/user/profile/` endpoint
- **`updateUIForUser()`**: Updates entire UI based on authentication status
- **Event Listeners**: 
  - Page load: Check authentication status
  - Window focus: Re-check when returning from login
  - Visibility change: Re-check on mobile browsers

## Authentication Flow

### 1. Page Load
```javascript
document.addEventListener('DOMContentLoaded', function() {
    updateUIForUser(); // Check auth status and update UI
});
```

### 2. Authentication Check
```javascript
async function getUserProfile() {
    const token = localStorage.getItem('access_token');
    const response = await fetch(`${API_BASE_URL}/api/user/profile/`, {
        headers: { 'Authorization': `Bearer ${token}` }
    });
    // Returns user data or null
}
```

### 3. UI Update
```javascript
if (user) {
    // Hide login prompt, show user status
    // Pre-fill and lock form fields
    // Show auto-filled labels
} else {
    // Show login prompt, hide user status
    // Reset form fields to editable
    // Hide auto-filled labels
}
```

### 4. Login Redirect
```javascript
// Store redirect URL and go to login
sessionStorage.setItem('redirectAfterLogin', window.location.href);
window.location.href = `/login/?next=${currentUrl}`;
```

### 5. Return from Login
```javascript
// Multiple detection methods:
window.addEventListener('focus', updateUIForUser);
document.addEventListener('visibilitychange', updateUIForUser);
```

## API Endpoint Used
- **Endpoint**: `GET /api/user/profile/`
- **Authentication**: Bearer token in Authorization header
- **Response**: User profile data including name and email
- **Error Handling**: 401 responses clear invalid tokens

## User Experience

### Before Fix:
1. User logs in successfully
2. Returns to Contact Us page
3. Still sees "please log in" message
4. Form fields are empty and editable

### After Fix:
1. User logs in successfully
2. Returns to Contact Us page
3. Sees "Logged in as: <EMAIL>"
4. Form fields are pre-filled and readonly
5. Shows "(Auto-filled)" labels

## Testing Scenarios

### Test 1: Not Logged In
1. Visit `/contact-us/` without login
2. **Expected**: Login prompt visible, form fields editable

### Test 2: Login Flow
1. Click "please log in" from contact page
2. Login successfully
3. **Expected**: Redirected back to contact page with user info pre-filled

### Test 3: Already Logged In
1. Login first
2. Visit `/contact-us/` directly
3. **Expected**: No login prompt, form pre-filled immediately

### Test 4: Token Expiry
1. Login and visit contact page (works)
2. Wait for token to expire
3. Refresh page
4. **Expected**: Login prompt appears, form becomes editable

## Browser Compatibility
- **localStorage**: Supported in all modern browsers
- **fetch API**: Supported in all modern browsers
- **Event Listeners**: Universal support
- **Async/Await**: Supported in modern browsers (IE11+ with polyfill)

## Security Considerations
- **Token Validation**: Invalid tokens are automatically cleared
- **API Errors**: Gracefully handled without breaking UI
- **XSS Protection**: No direct HTML injection, uses textContent
- **CSRF**: Not applicable for API calls with JWT tokens
