# Contact Us Authentication Debug Guide

This guide helps debug why the contact us page might still show login prompts after successful login.

## Debug Steps

### 1. Open Browser Developer Tools
- Press F12 or right-click → Inspect
- Go to Console tab
- Refresh the contact us page

### 2. Check Console Output
Look for these debug messages:

```
🚀 Page loaded, initializing contact form...
🔍 API_BASE_URL: http://**************:8001
🔍 LOCAL_BASE_URL: http://**************:8000
🔍 LocalStorage contents:
  - access_token: EXISTS/MISSING
  - refresh_token: EXISTS/MISSING  
  - user_info: EXISTS/MISSING
🔍 Starting UI update...
🔍 Authentication check: {hasToken: true/false, tokenLength: X, isAuthenticated: true/false}
```

### 3. Manual API Test
In the browser console, run:
```javascript
testUserAPI()
```

This will show:
- Token status
- API call details
- Response data
- Any errors

### 4. Check Network Tab
- Go to Network tab in DevTools
- Refresh page
- Look for call to `/api/user/profile/`
- Check status code and response

## Common Issues & Solutions

### Issue 1: No Token in localStorage
**Symptoms**: 
- Console shows "access_token: MISSING"
- Authentication check shows hasToken: false

**Solution**: 
- Login again
- Check if login page is properly storing tokens

### Issue 2: Token Expired (401 Error)
**Symptoms**:
- Console shows "Token expired (401), clearing storage"
- API call returns 401 status

**Solution**:
- Login again to get fresh token
- Check token expiration settings

### Issue 3: CORS Error
**Symptoms**:
- Console shows CORS-related errors
- Network tab shows failed requests

**Solution**:
- Check backend CORS settings
- Verify API_BASE_URL is correct

### Issue 4: Wrong API Response Format
**Symptoms**:
- API call succeeds but user data is null
- Console shows "API response indicates failure"

**Solution**:
- Check API response structure
- Verify backend user profile endpoint

### Issue 5: API Endpoint Not Found (404)
**Symptoms**:
- Network tab shows 404 for `/api/user/profile/`
- Console shows "API call failed with status: 404"

**Solution**:
- Verify backend API endpoint exists
- Check URL configuration

## Expected Flow

### Successful Authentication:
1. Page loads
2. Token found in localStorage
3. API call to `/api/user/profile/` succeeds
4. User data received and parsed
5. UI updated to show logged-in state
6. Form fields pre-filled and locked

### Failed Authentication:
1. Page loads
2. No token found OR API call fails
3. Fallback to localStorage user_info (if available)
4. If no fallback data, show login prompt
5. Form fields remain editable

## Manual Testing Commands

### Check Authentication Status:
```javascript
// Check if user appears authenticated
isAuthenticated()

// Get user profile
getUserProfile().then(user => console.log('User:', user))

// Update UI manually
updateUIForUser()

// Test API directly
testUserAPI()
```

### Check localStorage:
```javascript
// View all stored data
console.log('Access Token:', localStorage.getItem('access_token'));
console.log('User Info:', JSON.parse(localStorage.getItem('user_info') || 'null'));
```

### Clear localStorage (for testing):
```javascript
localStorage.removeItem('access_token');
localStorage.removeItem('refresh_token');
localStorage.removeItem('user_info');
location.reload();
```

## API Response Format

The backend should return:
```json
{
  "success": true,
  "user": {
    "email": "<EMAIL>",
    "first_name": "John",
    "last_name": "Doe"
  }
}
```

## Troubleshooting Checklist

- [ ] Backend server is running on correct port (8001)
- [ ] Frontend can access backend (no CORS errors)
- [ ] User is actually logged in (check login page)
- [ ] JWT token is stored in localStorage
- [ ] JWT token is not expired
- [ ] API endpoint `/api/user/profile/` exists and works
- [ ] API returns expected data format
- [ ] JavaScript console shows no errors
- [ ] Network requests are successful

## Contact Support

If issues persist after following this guide:
1. Copy all console output
2. Copy network tab details for failed requests
3. Note exact steps to reproduce the issue
4. Include browser and version information
