# HiSage Health Contact System

A complete contact system that allows users to send messages and administrators to manage them.

## Features

### 📧 Contact Us Page
- **Modern responsive design** with Bootstrap 5
- **Multiple inquiry types**: General, Technical Support, Partnership, Media, Other
- **Form validation** with real-time feedback
- **Rate limiting** to prevent spam (10 requests per minute per IP)
- **CSRF protection** for security

### 📋 Message Management (Admin Only)
- **List all messages** with pagination (20 per page)
- **Search functionality** by name, email, or message content
- **Filter by message type**
- **Detailed message view** with sender information
- **Admin authentication** required (staff users or Data Analysts group)

## Access URLs

### For Users
- **Contact Us Page**: `http://**************:8000/contact-us/`
- **Success Page**: `http://**************:8000/contact-us/success/`

### For Administrators
- **Message Management**: `http://**************:8000/message/`
- **Dashboard**: `http://**************:8000/dashboard/`

## Setup Instructions

### 1. Create Admin User (if not already done)
```bash
cd backend
python manage.py create_data_admin --email <EMAIL> --password SecurePassword123!
```

### 2. Apply Database Migrations (if needed)
```bash
cd app_frontend
python manage.py makemigrations inquiry
python manage.py migrate
```

### 3. Access the System
1. **Users** can visit the contact page and send messages
2. **Admins** must login first, then access the message management page

## Message Types

1. **General Inquiry** - General questions and information requests
2. **Technical Support** - Technical issues and support requests
3. **Partnership** - Business partnership inquiries
4. **Media Inquiry** - Press and media related questions
5. **Other** - Any other type of inquiry

## Admin Features

### Message List View
- **Search**: Search by sender name, email, or message content
- **Filter**: Filter messages by type
- **Pagination**: Navigate through multiple pages of messages
- **Message Details**: View full message content with sender information
- **Timestamps**: See when messages were received
- **Color-coded badges**: Different colors for different inquiry types

### Security
- **Authentication Required**: Only logged-in admin users can access
- **Permission Check**: Users must be staff or in "Data Analysts" group
- **Rate Limiting**: Contact form has rate limiting to prevent spam

## Database Schema

The system uses the existing `Inquiry` model with these fields:
- `name`: Sender's full name
- `email`: Sender's email address
- `phone`: Optional phone number
- `inquiry_type`: Type of inquiry (0-4)
- `description`: Message content (max 1500 characters)
- `datetime`: Timestamp when message was received

## Technical Details

### Frontend
- **Framework**: Django templates with Bootstrap 5
- **JavaScript**: Vanilla JS for form submission and CSRF handling
- **Styling**: Custom CSS with gradient backgrounds and modern design
- **Responsive**: Works on desktop and mobile devices

### Backend
- **Views**: Django class-based and function-based views
- **Forms**: Django ModelForm for data validation
- **Security**: CSRF protection, rate limiting, authentication
- **Database**: Uses existing Django ORM and database

## Troubleshooting

### Contact Form Issues
- **Form not submitting**: Check CSRF token and network connectivity
- **Rate limit error**: Wait a few minutes before trying again
- **Validation errors**: Ensure all required fields are filled

### Message Management Issues
- **Access denied**: Ensure user is logged in and has admin privileges
- **Messages not showing**: Check database for saved inquiries
- **Search not working**: Verify search parameters and try different terms

## Future Enhancements

Possible improvements for the system:
- Email notifications when new messages are received
- Message status tracking (read/unread, replied/pending)
- Reply functionality directly from the admin interface
- Export messages to CSV/Excel
- Message categories and tags
- Automated responses for common inquiries
