# Contact Us Back Navigation Fix

This document explains the fix for the Contact Us login back navigation issue.

## Problem Description

**Issue**: After logging in from Contact Us page, back button requires two clicks to return to Home:
1. User on Home page → clicks "Contact Us" → goes to Contact Us page
2. User clicks "please log in" → goes to Login page → logs in successfully  
3. User redirected back to Contact Us page → clicks browser back button
4. **Expected**: Return to Home page with 1 click
5. **Actual**: Requires 2 clicks to reach Home page

**Root Cause**: The Contact Us login link was storing Contact Us page as both the redirect target AND the original page, instead of storing the actual referrer (Home page) as the original page.

## Solution Implemented

### 1. Fixed Contact Us Login Link (`inquiry-create.html`)

#### Before Fix:
```javascript
// WRONG: Storing Contact Us as both redirect and original
sessionStorage.setItem('redirectAfterLogin', currentUrl);        // Contact Us
sessionStorage.setItem('originalPageBeforeLogin', currentUrl);   // Contact Us (WRONG!)
```

#### After Fix:
```javascript
// CORRECT: Store Contact Us as redirect, but referrer as original
const currentUrl = window.location.href;  // Contact Us page
const referrer = document.referrer;       // Home page (where user came from)

// Store current page as redirect target (where to go after login)
sessionStorage.setItem('redirectAfterLogin', currentUrl);

// Store the referrer as original page (where user came from before Contact Us)
if (referrer && !referrer.includes('/login/') && !referrer.includes('/contact-us/')) {
    sessionStorage.setItem('originalPageBeforeLogin', referrer);  // Home page
} else {
    sessionStorage.setItem('originalPageBeforeLogin', '/');       // Fallback to home
}
```

### 2. Enhanced Login Success Handler (`navigation-manager.js`)

#### Immediate Redirect Logic:
```javascript
handleLoginSuccess() {
    const redirectUrl = sessionStorage.getItem('redirectAfterLogin');
    const originalPage = sessionStorage.getItem('originalPageBeforeLogin');

    if (redirectUrl && originalPage) {
        const redirectPath = new URL(redirectUrl).pathname;  // '/contact-us/'
        const originalPath = new URL(originalPage).pathname; // '/'

        if (redirectPath !== originalPath) {
            // Different page redirect - set flag for immediate redirect
            sessionStorage.removeItem('redirectAfterLogin');
            sessionStorage.setItem('needsHistoryFix', 'true');

            // Navigate to Contact Us page
            window.location.replace(redirectUrl);
        }
    }
}
```

### 3. Immediate Redirect Post-Login Setup (`navigation-manager.js`)

#### Immediate Redirect Logic:
```javascript
setupPostLoginHistory() {
    const originalPage = sessionStorage.getItem('originalPageBeforeLogin');  // Home page
    const needsHistoryFix = sessionStorage.getItem('needsHistoryFix');       // 'true'
    const currentUrl = window.location.href;                                 // Contact Us page

    if (originalPage && needsHistoryFix === 'true') {
        console.log('🔧 Applying immediate redirect to original page');

        // Clear the flags
        sessionStorage.removeItem('needsHistoryFix');
        sessionStorage.removeItem('originalPageBeforeLogin');

        // Immediate redirect to original page using replace
        // This prevents the Contact Us page from fully loading and showing the login message
        console.log('🔄 Immediately redirecting to original page to avoid flash');
        window.location.replace(originalPage);  // Go directly to Home page

        return; // Exit early since we're redirecting
    }
}
```

## User Experience Flow (After Fix)

### Scenario: Home → Contact Us → Login → Home (Direct)
1. **User on Home page** → clicks "Contact Us"
2. **Browser history**: `[Home]`
3. **Contact Us page loads** → user clicks "please log in"
4. **System stores**:
   - `redirectAfterLogin = '/contact-us/'` (Contact Us page)
   - `originalPageBeforeLogin = '/'` (Home page - the referrer)
5. **Login page loads** → user logs in successfully
6. **Login success** → detects different page redirect → sets `needsHistoryFix = 'true'`
7. **Navigate to Contact Us** → `window.location.replace('/contact-us/')`
8. **Contact Us page starts loading** → `setupPostLoginHistory()` runs immediately:
   - Detects `needsHistoryFix = 'true'`
   - **Immediately redirects to Home page** → `window.location.replace('/')`
9. **User ends up on Home page** → **No back button needed, already at destination** ✅

## Browser History States

### Before Fix:
```
Navigation: Home → Contact Us → Login → Contact Us
History: [Home] → [Home, Contact Us] → [Home, Contact Us] (login replaced)
Back button: Contact Us → Contact Us (stuck) → requires manual navigation
```

### After Fix:
```
Navigation: Home → Contact Us → Login → Contact Us
History: [Home] → [Home, Contact Us] → [Home, Contact Us] (login replaced)
Post-login setup: History manipulation + popstate listener
Back button: Contact Us → (popstate listener) → Home ✅
```

## Technical Implementation Details

### 1. Referrer Detection
- **`document.referrer`**: Gets the URL of the page that navigated to current page
- **Validation**: Excludes login and contact-us URLs to prevent loops
- **Fallback**: Uses home page if no valid referrer

### 2. History Manipulation
- **`history.replaceState()`**: Modifies current history entry without adding new one
- **State Storage**: Stores original page reference in history state
- **Popstate Listener**: Handles back button clicks to navigate to original page

### 3. Session Storage Management
- **Clear Before Store**: Prevents conflicts with other login flows
- **Separate Values**: Redirect target vs. original page are different
- **Cleanup**: Values are cleared after processing

## Edge Cases Handled

### 1. Direct Contact Us Access
- **Scenario**: User types Contact Us URL directly
- **Referrer**: Empty or invalid
- **Fallback**: Uses home page as original page

### 2. Contact Us from Contact Us
- **Scenario**: User refreshes Contact Us page
- **Referrer**: Contact Us page itself
- **Handling**: Excluded by validation, falls back to home page

### 3. Multiple Login Flows
- **Scenario**: User starts different login flow before completing Contact Us login
- **Handling**: Session storage is cleared before storing new values

## Testing Scenarios

### Test Case 1: Home → Contact Us → Login → Back
1. Start on Home page
2. Click "Contact Us" link
3. Click "please log in" on Contact Us page
4. Complete login process
5. Click browser back button once
6. **Expected**: Return to Home page ✅

### Test Case 2: Profile → Contact Us → Login → Back
1. Start on Profile page
2. Navigate to Contact Us page
3. Click "please log in"
4. Complete login process
5. Click browser back button once
6. **Expected**: Return to Profile page ✅

### Test Case 3: Direct Contact Us → Login → Back
1. Type Contact Us URL directly in browser
2. Click "please log in"
3. Complete login process
4. Click browser back button once
5. **Expected**: Go to Home page (fallback) ✅

## Browser Compatibility

- **History API**: `history.replaceState()` supported in all modern browsers
- **Popstate Events**: Universal support for back button detection
- **Document Referrer**: Standard property available in all browsers
- **URL Constructor**: Modern browsers support URL parsing

## Debug Information

The implementation includes comprehensive logging:

```javascript
console.log('🔍 Current URL:', currentUrl);
console.log('🔍 Referrer:', referrer);
console.log('📝 Login context stored:', {
    redirectAfterLogin: currentUrl,
    originalPageBeforeLogin: sessionStorage.getItem('originalPageBeforeLogin')
});
console.log('🔙 Navigating to original page:', originalPage);
```

This fix ensures that users can navigate back from Contact Us page to their original location with a single back button click after logging in.
