# HiSage Health Dashboard

This dashboard provides administrative access to view user data, audio analysis results, and donation statistics.

## Setup Instructions

### 1. Create Admin User

Run this command in the backend directory to create an admin user:

```bash
cd backend
python manage.py create_data_admin --email "<EMAIL>" --password 'password'
```

### 2. Access Dashboard

1. **Frontend Dashboard URL**: http://**************:8000/dashboard/
2. **Login with the admin credentials you created**
3. **The dashboard will automatically load data from the backend APIs**

## Features

### Statistics Cards
- **Total Users**: Count of all registered users
- **Active Users**: Count of activated user accounts
- **Audio Analyses**: Total number of audio analysis records
- **Total Donations**: Sum of all completed donations

### Charts
- **User Registration Trend**: Monthly registration statistics over the past 12 months
- **Analysis Status Distribution**: Pie chart showing completed, processing, and failed analyses
- **Monthly Donation Trend**: Bar chart of donation amounts by month
- **Daily Analysis Volume**: Bar chart showing daily analysis counts for the past 30 days

### Recent Activities
- **Recent Users**: Last 10 registered users with activation status
- **Recent Analyses**: Last 10 audio analyses with status and user information
- **Recent Donations**: Last 10 donations with amounts and status

## API Endpoints

The dashboard uses these backend API endpoints:

- `GET /api/dashboard/user-stats/` - User statistics
- `GET /api/dashboard/analysis-stats/` - Audio analysis statistics  
- `GET /api/dashboard/donation-stats/` - Donation statistics
- `GET /api/dashboard/recent-activities/` - Recent activity data

## Security

- **Authentication Required**: Users must be logged in with admin privileges
- **Permission Check**: Only users with `is_staff=True` or in the `Data Analysts` group can access
- **JWT Token Authentication**: Uses the same authentication system as the main application

## Auto-Refresh

The dashboard automatically refreshes data every 5 minutes to keep statistics current.

## Troubleshooting

### "Authentication Required" Message
- Make sure you're logged in to the frontend application
- Verify your user account has admin privileges
- Check that your JWT token hasn't expired

### "Access Denied" Error
- Ensure your user account has `is_staff=True` or is in the `Data Analysts` group
- Contact a system administrator to grant proper permissions

### Data Not Loading
- Check that the backend API is running on http://**************:8001
- Verify CORS settings allow requests from the frontend
- Check browser console for any JavaScript errors

## Technical Details

- **Frontend**: HTML/CSS/JavaScript with Bootstrap 5 and Chart.js
- **Backend**: Django REST Framework with JWT authentication
- **Database**: Uses the same database as the main application
- **Charts**: Powered by Chart.js library
- **Responsive**: Works on desktop and mobile devices
