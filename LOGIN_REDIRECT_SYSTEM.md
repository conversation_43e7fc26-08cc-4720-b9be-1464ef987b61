# Login Redirect System

This document explains how the login redirect system works to ensure users are redirected back to their original page after logging in.

## How It Works

### 1. User Flow
1. User visits Contact Us page (`/contact-us/`) without being logged in
2. User sees message: "You can contact us without logging in, but if you have an account, please log in for a better experience"
3. User clicks "please log in" link
4. System stores current page URL and redirects to login page
5. User logs in successfully
6. System redirects user back to Contact Us page
7. Contact Us page now shows user's information pre-filled

### 2. Technical Implementation

#### Contact Us Page (`/contact-us/`)
- **Detection**: Checks if user is authenticated using `{% if not user %}`
- **Link Handler**: JavaScript captures click on "please log in" link
- **Storage**: Stores current URL in `sessionStorage.setItem('redirectAfterLogin', window.location.href)`
- **Redirect**: Redirects to `/login/?next=${currentUrl}` with URL parameter as backup

#### Login Page (`/login/`)
- **Parameter Handling**: Checks for `next` parameter in URL
- **Storage**: If `next` parameter exists, stores it in sessionStorage
- **Login Success**: After successful login, checks for stored redirect URL
- **Redirect Logic**: 
  ```javascript
  const redirectUrl = sessionStorage.getItem('redirectAfterLogin');
  if (redirectUrl) {
      sessionStorage.removeItem('redirectAfterLogin');
      window.location.href = redirectUrl;
  } else {
      window.location.href = '/'; // Default to home page
  }
  ```

### 3. Dual Method Approach

The system uses two methods to ensure reliability:

1. **Primary Method**: SessionStorage
   - Stores redirect URL in browser's sessionStorage
   - Persists during the login process
   - Automatically cleared after use

2. **Backup Method**: URL Parameter
   - Passes redirect URL as `next` parameter
   - Handles cases where sessionStorage might not work
   - URL-encoded for safety

### 4. Security Considerations

- **URL Validation**: Only allows redirects to same-origin URLs
- **Encoding**: URLs are properly encoded/decoded to prevent injection
- **Cleanup**: Redirect URLs are removed from storage after use

## Testing the System

### Test Case 1: Contact Us → Login → Contact Us
1. Visit `http://**************:8000/contact-us/` (not logged in)
2. Click "please log in" link
3. Should redirect to login page with next parameter
4. Log in with valid credentials
5. Should redirect back to contact us page
6. Contact form should show pre-filled user information

### Test Case 2: Direct Login
1. Visit `http://**************:8000/login/` directly
2. Log in with valid credentials
3. Should redirect to home page (default behavior)

### Test Case 3: Login with Next Parameter
1. Visit `http://**************:8000/login/?next=/contact-us/`
2. Log in with valid credentials
3. Should redirect to contact us page

## Files Modified

1. **app_frontend/templates/html/inquiry/inquiry-create.html**
   - Added JavaScript to handle login link clicks
   - Store redirect URL in sessionStorage
   - Pass next parameter to login page

2. **app_frontend/project/views.py**
   - Updated `login_page` view to handle next parameter
   - Pass next_url to template context

3. **app_frontend/templates/html/login.html**
   - Added JavaScript to handle next parameter
   - Store redirect URL from URL parameter
   - Updated language to English

## Browser Compatibility

- **SessionStorage**: Supported in all modern browsers
- **URL Parameters**: Universal support
- **Fallback**: If sessionStorage fails, URL parameter method provides backup

## Error Handling

- If redirect URL is invalid or missing, defaults to home page
- If sessionStorage is not available, URL parameter method is used
- All URLs are validated before redirect to prevent security issues
