{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Cognitive Health Analysis Report</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/css/all.min.css">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        /* Professional Medical Report Style */
        body {
            font-family: 'Times New Roman', Times, serif;
            background-color: #EAEAEA; /* Light grey background to emphasize the page */
            margin: 0;
            padding: 2rem;
            color: #000000;
            -webkit-font-smoothing: auto;
        }

        .controls {
            max-width: 900px;
            margin: 0 auto 1.5rem auto;
            display: flex;
            justify-content: space-between;
            gap: 1rem;
        }

        .control-btn {
            font-family: 'Times New Roman', Times, serif; /* Consistent font */
            text-decoration: none;
            color: #000;
            background-color: #FFFFFF;
            border: 1px solid #000;
            padding: 0.5rem 1rem;
            border-radius: 0; /* Sharp corners for formal look */
            font-size: 0.9rem;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: background-color 0.2s;
        }
        .control-btn:hover {
            background-color: #F0F0F0;
        }

        .report-page {
            max-width: 900px;
            margin: 0 auto;
            background: #FFFFFF;
            border: 1px solid #000000;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .report-header {
            padding: 2rem;
            border-bottom: 2px solid #000000;
        }

        .header-top {
            display: flex;
            align-items: flex-start;
            justify-content: space-between;
            margin-bottom: 2rem;
        }

        .header-logo {
            width: 80px;
            height: 80px;
            object-fit: contain;
            flex-shrink: 0;
        }

        .header-main {
            flex: 1;
            text-align: center;
            margin: 0 2rem;
        }
        .header-main h1 {
            font-family: 'Times New Roman', Times, serif;
            font-size: 1.8rem;
            margin: 0 0 0.25rem 0;
            font-weight: bold;
        }
        .header-main p {
            font-size: 1rem;
            margin: 0;
            font-weight: bold;
            color: #333;
        }

        .demographics-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.95rem;
            border: 1px solid #000; /* Add outer border */
        }
        .demographics-table td {
            padding: 0.35rem 0.5rem;
            border: 1px solid #CCC; /* Lighter inner borders */
        }
        .demographics-table .label {
            font-weight: bold;
            white-space: nowrap;
            color: #000;
        }
        .demographics-table .value {
            color: #000;
        }

        .report-body {
            padding: 2rem;
            font-family: 'Times New Roman', Times, serif;
        }

        .report-section {
            margin-bottom: 2.5rem;
        }
        .section-title {
            font-family: 'Times New Roman', Times, serif;
            font-size: 1.4rem; /* Larger section titles */
            font-weight: bold;
            color: #000;
            border-bottom: 1px solid #000;
            padding-bottom: 0.5rem;
            margin: 0 0 1.5rem 0;
        }

        .results-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 1rem;
            margin-bottom: 2rem;
            border: 1px solid black;
        }
        .results-table th, .results-table td {
            border: 1px solid black;
            padding: 0.75rem;
            text-align: left;
        }
        .results-table th {
            font-family: 'Times New Roman', Times, serif;
            background-color: #F0F0F0; /* Light grey header */
            font-weight: bold;
        }
        .results-table .result-value {
            font-weight: bold;
            text-align: center;
        }
        .results-table .result-assessment {
             font-style: normal; /* Remove italics for a more formal look */
        }

        .transcription-box {
            background: #F8F8F8;
            border: 1px solid #CCC;
            border-left: 4px solid #000000;
            padding: 1rem 1.5rem;
            margin-top: 1rem;
        }
        .transcription-box h4 {
             font-family: 'Times New Roman', Times, serif;
             font-weight: bold;
        }
        .transcription-content {
            margin: 0;
            line-height: 1.7;
            font-style: italic;
            color: #000;
            font-family: 'Times New Roman', Times, serif;
            font-size: 1rem;
        }

        /* Enhanced styles for colored transcription */
        .transcription-content .word-normal {
            color: #333;
        }
        .transcription-content .word-hesitation {
            color: #ff6b35;
            background-color: rgba(255, 107, 53, 0.1);
            padding: 1px 3px;
            border-radius: 3px;
        }
        .transcription-content .word-repetition {
            color: #e74c3c;
            background-color: rgba(231, 76, 60, 0.1);
            padding: 1px 3px;
            border-radius: 3px;
            text-decoration: underline;
        }
        .transcription-content .word-pause {
            color: #9b59b6;
            background-color: rgba(155, 89, 182, 0.1);
            padding: 1px 3px;
            border-radius: 3px;
            font-weight: bold;
        }
        .transcription-content .word-filler {
            color: #f39c12;
            background-color: rgba(243, 156, 18, 0.1);
            padding: 1px 3px;
            border-radius: 3px;
            font-style: normal;
        }
        .transcription-content .word-unclear {
            color: #95a5a6;
            background-color: rgba(149, 165, 166, 0.1);
            padding: 1px 3px;
            border-radius: 3px;
            text-decoration: line-through;
        }
        .transcription-content .word-emphasis {
            color: #27ae60;
            background-color: rgba(39, 174, 96, 0.1);
            padding: 1px 3px;
            border-radius: 3px;
            font-weight: bold;
        }
        .transcription-content .word-error {
            color: #c0392b;
            background-color: rgba(192, 57, 43, 0.1);
            padding: 1px 3px;
            border-radius: 3px;
            border: 1px solid rgba(192, 57, 43, 0.3);
        }



        /* Responsive styles for transcription */
        @media (max-width: 768px) {
            .transcription-content {
                font-size: 0.9rem;
                line-height: 1.6;
            }
        }

        @media (max-width: 480px) {
            .transcription-content {
                font-size: 0.85rem;
            }
        }

        .features-grid {
            margin-top: 1.5rem;
        }
        .feature-category {
            margin-bottom: 2rem;
        }
        .feature-category h4 {
            font-family: 'Times New Roman', Times, serif;
            font-size: 1.1rem; /* Slightly larger category titles */
            font-weight: bold;
            margin: 0 0 1rem 0;
            color: #000;
        }
        .feature-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }
        .feature-table th, .feature-table td {
            border: 1px solid #E0E0E0;
            padding: 0.5rem;
            text-align: left;
        }
        .feature-table th {
            background-color: #F8F8F8;
            font-weight: bold;
        }
        .feature-table td:nth-child(2), .feature-table th:nth-child(2) {
            text-align: center;
            width: 20%;
            position: relative; /* 为箭头定位做准备 */
        }
        .feature-table td:nth-child(3), .feature-table th:nth-child(3) {
            text-align: center;
            width: 25%;
        }

        .abnormal-indicator {
            color: #000000; /* 黑色箭头 */
            font-weight: bold;
            position: absolute;
            right: 10px; /* 距离单元格右边缘的距离 */
            top: 50%;
            transform: translateY(-50%);
        }

        .reference-range {
            font-size: 0.85rem;
            color: #555;
            margin-left: 0.5rem;
            font-weight: normal;
        }
        
        /* Simple reliable tooltip */
        .tooltip-container {
            position: relative;
            display: inline-flex;
            vertical-align: middle;
            line-height: 1;
            align-items: center;
        }
        
        .tooltip-container .question-mark {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            margin-left: 5px;
            background-color: #3498db;
            color: white;
            font-weight: bold;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            text-align: center;
            font-size: 10px;
            cursor: help;
        }

        /* Enhanced tooltip using pseudo-element */
        .tooltip-container:hover::before {
            position: absolute;
            z-index: 1000;
            bottom: 100%;
            margin-bottom: 10px;
            left: 50%;
            transform: translateX(-50%);
            width: 300px;
            padding: 12px 15px;
            background-color: #2c3e50;
            color: #fff;
            text-align: left;
            font-family: 'Times New Roman', Times, serif;
            font-size: 13px;
            font-weight: normal;
            line-height: 1.5;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.25);
            letter-spacing: 0.2px;
        }
        
        /* MMSE tooltip content */
        .tooltip-container.mmse-tooltip:hover::before {
            content: attr(data-tooltip-text);
        }
        
        /* Percentile tooltip specific content */
        .tooltip-container.percentile-tooltip:hover::before {
            content: attr(data-tooltip-text);
            width: 320px;
        }
        
        /* Model tooltip specific content */
        .tooltip-container.model-tooltip:hover::before {
            content: attr(data-tooltip-text);
            width: 280px;
        }
        
        /* RMSE tooltip specific content */
        .tooltip-container.rmse-tooltip:hover::before {
            content: attr(data-tooltip-text);
            width: 320px;
        }
        
        /* Pearson tooltip specific content */
        .tooltip-container.pearson-tooltip:hover::before {
            content: attr(data-tooltip-text);
            width: 320px;
        }

        /* Transcription tooltip specific content */
        .tooltip-container.transcription-tooltip:hover::before {
            content: attr(data-tooltip-text);
            width: 350px;
        }

        /* Feature tooltip specific content */
        .tooltip-container.feature-tooltip:hover::before {
            content: attr(data-tooltip-text);
            width: 320px;
        }
        
        /* Arrow for tooltip */
        .tooltip-container:hover::after {
            content: "";
            position: absolute;
            bottom: 100%;
            left: 50%;
            margin-left: -6px;
            border-width: 6px;
            border-style: solid;
            border-color: transparent transparent #2c3e50 transparent;
            transform: rotate(180deg);
        }

        .methodology-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
            border: 1px solid #CCC;
            padding: 1rem;
            background: #F8F8F8;
        }
        .methodology-grid .label {
            font-weight: bold;
        }

        .report-footer {
            margin-top: 3rem;
            padding: 1.5rem 2rem;
            border-top: 2px solid #000;
            font-size: 0.8rem;
            color: #333;
        }
        .disclaimer {
            line-height: 1.6;
            margin-bottom: 1rem;
        }
        .contact-info {
            line-height: 1.6;
            margin-bottom: 1rem;
            margin-top: 1rem;
        }

        @media (max-width: 800px) {
            body { padding: 1rem; }
            .features-list { column-count: 1; }
            .controls { justify-content: space-between; }

            .header-top {
                flex-direction: column;
                align-items: center;
                text-align: center;
            }

            .header-logo {
                width: 60px;
                height: 60px;
                margin-bottom: 1rem;
            }

            .header-main {
                margin: 0;
            }
        }

        @media print {
            body { background-color: #fff; padding: 0; }
            .controls { display: none; }
            .report-page {
                box-shadow: none;
                border: none;
                max-width: 100%;
            }
        }
    </style>
</head>
<body>

    <div class="controls">
        <button id="download-btn" class="control-btn">
            <i class="fa-solid fa-file-pdf"></i> Download PDF Report
        </button>
    </div>

    <div class="report-page">
        <header class="report-header">
            <div class="header-top">
                <img src="{% static 'logos/logo1.png' %}" alt="Cognitive Health Logo" class="header-logo">
                <div class="header-main">
                    <h1>Cognitive Health Speech Analysis</h1>
                    <p>CONFIDENTIAL SCIENTIFIC REPORT</p>
                </div>
                <div style="width: 80px;"></div> <!-- 占位符保持居中 -->
            </div>
            <table class="demographics-table">
                <tbody>
                    <tr>
                        <td class="label">Audio File Name:</td>
                        <td class="value" id="subject-id"></td>
                        <td class="label">Date of Report:</td>
                        <td class="value" id="report-date"></td>
                    </tr>
                    <tr>
                        <td class="label">This Audio is Spoken by:</td>
                        <td class="value" id="subject-relationship"></td>
                        <td class="label">Occupation:</td>
                        <td class="value" id="subject-occupation"></td>
                    </tr>
                    <tr>
                        <td class="label">Age:</td>
                        <td class="value" id="subject-age" colspan="3"></td>
                    </tr>
                </tbody>
            </table>
        </header>

        <main class="report-body">
            <section class="report-section">
                <h3 class="section-title">Prediction Results</h3>
                <table class="results-table">
                    <thead>
                        <tr>
                            <th style="width: 50%; text-align: center;">
                                <strong>Predicted MMSE Score</strong> (from speech)
                                <span class="tooltip-container mmse-tooltip" style="position: relative; top: 0;">
                                    <span class="question-mark">?</span>
                                </span>
                            </th>
                            <th style="width: 50%; text-align: center;">
                                Percentile
                                <span class="tooltip-container percentile-tooltip" style="position: relative; top: 0;" data-tooltip-text="The percentile rank of the predicted MMSE score among all test subjects. A higher percentile indicates better cognitive function.">
                                    <span class="question-mark">?</span>
                                </span>
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="result-value" id="result-mmse"></td>
                            <td class="result-value" id="result-percentile"></td>
                        </tr>
                    </tbody>
                </table>
            </section>

            <section class="report-section">
                 <h3 class="section-title">Technical Details</h3>
                <table class="results-table">
                    <thead>
                        <tr>
                            <th style="width: 50%; text-align: center;">
                                Model
                                <span class="tooltip-container model-tooltip" style="position: relative; top: 0;">
                                    <span class="question-mark">?</span>
                                </span>
                            </th>
                            <th style="width: 50%; text-align: center;">Model Performance</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="result-value" id="tech-model"></td>
                            <td>
                                <div style="margin-bottom: 10px;">
                                    <strong>RMSE:</strong> 
                                    <span id="tech-rmse" style="vertical-align: middle;"></span>
                                    <span class="tooltip-container rmse-tooltip">
                                        <span class="question-mark">?</span>
                                    </span>
                                </div>
                                <div>
                                    <strong>Pearson correlation coefficient:</strong> 
                                    <span id="tech-pearson" style="vertical-align: middle;"></span>
                                    <span class="tooltip-container pearson-tooltip">
                                        <span class="question-mark">?</span>
                                    </span>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </section>

            <section class="report-section">
                <h3 class="section-title">Speech Analysis</h3>
                <div class="transcription-box">
                    <h4>Enhanced Transcription with Linguistic Analysis
                        <span class="tooltip-container transcription-tooltip" style="position: relative; top: 0;">
                            <span class="question-mark">?</span>
                        </span>
                    </h4>
                    <div id="result-transcription" class="transcription-content"></div>
                </div>
            </section>

            <section class="report-section">
                <h3 class="section-title">Feature Analysis</h3>
                <div class="features-grid" id="features-container">
                    <!-- JS will inject feature categories and lists here -->
                </div>
            </section>

        </main>

        <footer class="report-footer">
            <p class="disclaimer">
                <strong>MEDICAL DISCLAIMER:</strong> This computational analysis report is generated using artificial intelligence algorithms for research and clinical decision support purposes. The acoustic and linguistic biomarkers presented herein are derived from automated speech analysis and should be interpreted within the context of comprehensive clinical assessment. This report does not constitute a medical diagnosis, clinical recommendation, or therapeutic intervention. The findings require validation through standardized neuropsychological evaluation and clinical correlation by qualified healthcare professionals. Healthcare providers should exercise clinical judgment when integrating these results with patient history, physical examination, and other diagnostic modalities. This technology is intended as an adjunctive tool and should not replace established clinical protocols or professional medical expertise in the assessment of cognitive function.
            </p>
            <p class="contact-info">
                <strong>CONTACT INFORMATION:</strong> For any technical support and collaboration, <NAME_EMAIL>.
            </p>
            <p id="footer-id"></p>
        </footer>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 1. Get the data from Session Storage
            const analysisDetailsString = sessionStorage.getItem('analysisDetails');
            
            if (!analysisDetailsString) {
                document.body.innerHTML = '<h1>Error: No analysis data found. Please return to the history page and select a report.</h1>';
                return;
            }

            // 2. Safely parse the data (it's nested JSON)
            let item, analysisResult;
            try {
                item = JSON.parse(analysisDetailsString);
                analysisResult = item.result && typeof item.result === 'string' 
                    ? JSON.parse(item.result) 
                    : (item.result || {});
            } catch (e) {
                console.error("Failed to parse analysis data:", e);
                document.body.innerHTML = `<h1>Error: Could not display analysis data. The data may be corrupted.</h1>`;
                return;
            }

            // 3. Helper functions for formatting
            const formatValue = (value, fallback = 'Not provided') => value ?? fallback;
            
            const formatRelationship = (value) => {
                if (!value) return 'Not provided';
                const map = { 'my_self': 'Myself', 'my_father': 'My Father', 'my_mother': 'My Mother', 'my_father_in_law': 'My Father in Law', 'my_mother_in_law': 'My Mother in Law', 'my_grandfather': 'My Grandfather', 'my_grandmother': 'My Grandmother', 'my_friend': 'My Friend' };
                return map[value] || value;
            };

            const formatOccupation = (value) => {
                if (!value) return 'Not provided';
                const map = { 'student': 'Student', 'retired': 'Retired', 'unemployed': 'Unemployed' };
                return map[value] || value.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
            };

            // 标准的数值格式化函数 - 以10为底的科学记数法，保留3位有效数字
            const formatNumber = (value) => {
                const num = parseFloat(value);
                if (isNaN(num)) return 'Not provided';

                // 处理零值
                if (num === 0) return '0';

                // 处理整数
                if (Number.isInteger(num) && Math.abs(num) < 1000) {
                    return num.toString();
                }

                const absNum = Math.abs(num);

                // 判断是否需要科学记数法：过大(≥1000)或过小(<0.01)的数值
                if (absNum >= 1000 || (absNum < 0.01 && absNum !== 0)) {
                    // 使用标准的科学记数法格式，保留3位有效数字
                    const scientificStr = num.toPrecision(3); // 保留3位有效数字

                    // 如果toPrecision返回的是科学记数法格式
                    if (scientificStr.includes('e')) {
                        const [mantissa, exponentStr] = scientificStr.split('e');
                        const exponent = parseInt(exponentStr);
                        return `${mantissa} × 10<sup>${exponent}</sup>`;
                    } else {
                        // 如果toPrecision没有返回科学记数法，手动转换
                        const exponent = Math.floor(Math.log10(absNum));
                        const mantissa = (num / Math.pow(10, exponent)).toPrecision(3);
                        return `${mantissa} × 10<sup>${exponent}</sup>`;
                    }
                }

                // 对于正常范围的数值，保留3位有效数字
                return parseFloat(num.toPrecision(3)).toString();
            };

            // 纯文本版本的格式化函数（用于PDF等）
            const formatNumberForText = (value) => {
                const num = parseFloat(value);
                if (isNaN(num)) return 'Not provided';

                // 处理零值
                if (num === 0) return '0';

                // 处理整数
                if (Number.isInteger(num) && Math.abs(num) < 1000) {
                    return num.toString();
                }

                const absNum = Math.abs(num);

                // 判断是否需要科学记数法：过大(≥1000)或过小(<0.01)的数值
                if (absNum >= 1000 || (absNum < 0.01 && absNum !== 0)) {
                    // 使用标准的科学记数法格式，保留3位有效数字
                    const scientificStr = num.toPrecision(3); // 保留3位有效数字

                    let mantissa, exponent;

                    // 如果toPrecision返回的是科学记数法格式
                    if (scientificStr.includes('e')) {
                        [mantissa, exponent] = scientificStr.split('e');
                        exponent = parseInt(exponent);
                    } else {
                        // 如果toPrecision没有返回科学记数法，手动转换
                        exponent = Math.floor(Math.log10(absNum));
                        mantissa = (num / Math.pow(10, exponent)).toPrecision(3);
                    }

                    // 转换指数为Unicode上标字符
                    const superscriptMap = {
                        '0': '⁰', '1': '¹', '2': '²', '3': '³', '4': '⁴',
                        '5': '⁵', '6': '⁶', '7': '⁷', '8': '⁸', '9': '⁹',
                        '-': '⁻', '+': '⁺'
                    };
                    const superscriptExp = exponent.toString().replace(/[0-9+-]/g, char => superscriptMap[char] || char);

                    // 格式化为标准的10为底科学记数法
                    return `${mantissa} × 10${superscriptExp}`;
                }

                // 对于正常范围的数值，保留3位有效数字
                return parseFloat(num.toPrecision(3)).toString();
            };



            const formatDate = (dateString) => {
                if(!dateString) return 'Not provided';
                const date = new Date(dateString);
                const day = date.getDate().toString().padStart(2, '0');
                const month = (date.getMonth() + 1).toString().padStart(2, '0');
                const year = date.getFullYear();
                return `${day}-${month}-${year}`;
            };

            // Helper function to escape HTML for security when handling plain text
            function escapeHtml(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }

            // 4. Populate the document with data
            
            // Header Demographics
            document.getElementById('subject-id').textContent = formatValue(item.filename);
            document.getElementById('report-date').textContent = formatDate(new Date());
            document.getElementById('subject-age').textContent = formatValue(item.age);
            document.getElementById('subject-relationship').textContent = formatRelationship(item.relationship);
            document.getElementById('subject-occupation').textContent = formatOccupation(item.occupation);

            // Test Results Table
            document.getElementById('result-mmse').textContent = formatNumber(analysisResult['Predicted mmse score']);
            document.getElementById('result-percentile').textContent = formatValue(analysisResult['Percentile']) + '%';
            
            // Enhanced Transcription with Colors
            const transcriptionElement = document.getElementById('result-transcription');
            const coloredTranscription = analysisResult['Transcribed with color'];

            // Update transcription tooltip with audio file name
            const transcriptionTooltip = document.querySelector('.transcription-tooltip');
            const audioFileName = item.filename;
            if (transcriptionTooltip) {
                const tooltipText = `The automatic transcription of ${audioFileName}. The highlighted words were analyzed as crucial for making predictions.`;
                transcriptionTooltip.setAttribute('data-tooltip-text', tooltipText);
            }

            if (coloredTranscription && coloredTranscription.trim() !== '') {
                // Use innerHTML to render the colored HTML content
                transcriptionElement.innerHTML = coloredTranscription;
            } else {
                // Fallback to regular transcription if colored version is not available
                const regularTranscription = analysisResult['Transcribed'];
                if (regularTranscription && regularTranscription.trim() !== '') {
                    transcriptionElement.innerHTML = `<span class="word-normal">${escapeHtml(regularTranscription)}</span>`;
                } else {
                    transcriptionElement.innerHTML = '<span class="word-unclear">Transcription not available</span>';
                }
            }

            // Biomarkers
            const featuresContainer = document.getElementById('features-container');
            const features = analysisResult['Selected features'] || {};
            featuresContainer.innerHTML = '';
            
            if (Object.keys(features).length === 0) {
                featuresContainer.innerHTML = '<p>No biomarker data available for this analysis.</p>';
            } else {
                let categoryKeys = Object.keys(features);
                const is10Index = categoryKeys.indexOf('is10');
                if (is10Index > -1) {
                    const is10Key = categoryKeys.splice(is10Index, 1)[0];
                    categoryKeys.push(is10Key);
                }

                categoryKeys.forEach(category => {
                    const featureList = features[category];
                    
                    if (!Array.isArray(featureList) || featureList.length === 0) return;

                    const categoryDiv = document.createElement('div');
                    categoryDiv.className = 'feature-category';

                    const title = document.createElement('h4');
                    let categoryName = category.replace(/_/g, ' ');
                    // Apply specific category name mappings
                    if (categoryName === 'digipsych prosody feats') {
                        categoryName = 'Prosodic';
                    } else if (categoryName === 'lexicosyntactic') {
                        categoryName = 'Linguistic';
                    } else if (categoryName === 'is10') {
                        categoryName = 'Acoustic';
                    }
                    title.textContent = categoryName + ' Features';
                    categoryDiv.appendChild(title);

                    const table = document.createElement('table');
                    table.className = 'feature-table';

                    const thead = table.createTHead();
                    const headerRow = thead.insertRow();
                    headerRow.innerHTML = '<th>Feature</th><th>Value</th><th>Reference Range</th>';

                    const tbody = table.createTBody();
                    
                    featureList.forEach((featureObject, index) => {
                        const name = featureObject['feature name'];
                        const value = featureObject['value'];
                        const lower = featureObject['clsi lower'];
                        const upper = featureObject['clsi upper'];
                        const briefIntroduction = featureObject['brief introduction'];

                        if (name === undefined || value === undefined) return;

                        const row = tbody.insertRow();

                        // 创建特征名称单元格，包含提示功能
                        const nameCell = row.insertCell();
                        const displayName = name.replace(/_/g, ' ');

                        if (briefIntroduction && briefIntroduction.trim() !== '') {
                            // 如果有简介，添加提示
                            const uniqueId = `feature-${category}-${index}`;
                            nameCell.innerHTML = `
                                ${displayName}
                                <span class="tooltip-container feature-tooltip" data-tooltip-text="${escapeHtml(briefIntroduction)}" style="position: relative; top: 0;">
                                    <span class="question-mark">?</span>
                                </span>
                            `;
                        } else {
                            // 如果没有简介，只显示名称
                            nameCell.textContent = displayName;
                        }
                        
                        const valueCell = row.insertCell();
                        let indicator = '';
                        if (lower !== undefined && upper !== undefined) {
                            const numValue = parseFloat(value);
                            const numLower = parseFloat(lower);
                            const numUpper = parseFloat(upper);
                            if (!isNaN(numValue)) {
                                if (numValue > numUpper) {
                                    indicator = '<span class="abnormal-indicator">▲</span>'; // 实心三角形向上箭头
                                } else if (numValue < numLower) {
                                    indicator = '<span class="abnormal-indicator">▼</span>'; // 实心三角形向下箭头
                                }
                            }
                        }
                        valueCell.innerHTML = `<div style="text-align: center; width: 100%;">${formatNumber(value)}</div>${indicator}`;
                        
                        const refCell = row.insertCell();
                        if (lower !== undefined && upper !== undefined) {
                            refCell.innerHTML = `${formatNumber(lower)} - ${formatNumber(upper)}`;
                        } else {
                            refCell.textContent = 'Not provided';
                        }
                    });
 
                    if (tbody.rows.length > 0) {
                        categoryDiv.appendChild(table);
                        featuresContainer.appendChild(categoryDiv);
                    }
                });
            }
            
            // Methodology
            document.getElementById('tech-model').textContent = formatValue(analysisResult['Model']);
            
            // 处理 Model performance 数据
            const modelPerformance = analysisResult['Model performance'] || {};
            if (typeof modelPerformance === 'object') {
                // 如果是对象格式，分别获取 RMSE 和 Pearson correlation coefficient
                document.getElementById('tech-rmse').innerHTML = formatNumber(modelPerformance['RMSE']);
                document.getElementById('tech-pearson').innerHTML = formatNumber(modelPerformance['Pearson correlation coefficient']);
            } else {
                // 如果是旧格式（直接是 RMSE 值），则只设置 RMSE
                document.getElementById('tech-rmse').innerHTML = formatNumber(modelPerformance);
                document.getElementById('tech-pearson').innerHTML = 'Not provided';
            }
            
            // Footer
            document.getElementById('footer-id').textContent = `Report for File Name: ${formatValue(item.filename)}`;
            
            // Initialize tooltips with a simpler approach
            document.querySelectorAll('.tooltip-container').forEach(container => {
                const questionMark = container.querySelector('.question-mark');
                // The new tooltip is handled by pseudo-elements, so no need to add event listeners here
            });
            
            // 设置提示内容
            // 为MMSE提示设置内容
            const mmseTooltip = document.querySelector('.mmse-tooltip');
            if (mmseTooltip) {
                const mmseValue = formatNumber(analysisResult['Predicted mmse score']);
                mmseTooltip.setAttribute('data-tooltip-text', 
                    `The Mini-Mental State Examination (MMSE) score is predicted by analyzing the speech. The predicted MMSE score is ${mmseValue}. An MMSE score of 23 or lower indicates cognitive impairment.`
                );
            }
            
            // 为百分位提示设置动态内容
            const percentileValue = formatValue(analysisResult['Percentile']);
            const percentileTooltip = document.querySelector('.percentile-tooltip');
            if (percentileTooltip) {
                percentileTooltip.setAttribute('data-tooltip-text', 
                    `The percentile ranking of the predicted MMSE scores within the studied population. The predicted MMSE score exceeds that of ${percentileValue}% of the studied population.`
                );
            }
            
            // 为模型提示设置动态内容
            const modelValue = formatValue(analysisResult['Model']);
            const modelTooltip = document.querySelector('.model-tooltip');
            if (modelTooltip) {
                modelTooltip.setAttribute('data-tooltip-text', 
                    `The model used to predict the MMSE score: ${modelValue}.`
                );
            }
            
            // 处理 RMSE 提示
            // 获取 RMSE 值
            let rmseValue;
            if (typeof modelPerformance === 'object' && 'RMSE' in modelPerformance) {
                rmseValue = formatNumber(modelPerformance['RMSE']);
            } else {
                rmseValue = formatNumber(modelPerformance); // 旧格式直接是 RMSE 值
            }
            
            // 设置 RMSE 提示文本
            const rmseTooltip = document.querySelector('.rmse-tooltip');
            if (rmseTooltip) {
                rmseTooltip.setAttribute('data-tooltip-text', 
                    `The root mean square error (RMSE) of the model. It indicates that the average absolute bias between the predicted MMSE scores and the actual MMSE scores is ${rmseValue}. A lower RMSE value indicates higher accuracy in predicting the MMSE scores.`
                );
            }

            // 处理 Pearson correlation coefficient 提示
            let pearsonValue = 'Not provided';
            if (typeof modelPerformance === 'object' && 'Pearson correlation coefficient' in modelPerformance) {
                pearsonValue = formatNumber(modelPerformance['Pearson correlation coefficient']);
            }
            const pearsonTooltip = document.querySelector('.pearson-tooltip');
            if (pearsonTooltip) {
                pearsonTooltip.setAttribute('data-tooltip-text', 
                    `The Pearson correlation coefficient of the model. It indicates that the correlation between the predicted MMSE scores and the actual MMSE scores is ${pearsonValue}. A higher Pearson correlation coefficient (maximum is 1.0) indicates higher accuracy in predicting the MMSE scores.`
                );
            }

            // 5. Download functionality
            document.getElementById('download-btn').addEventListener('click', () => {
                // 显示加载指示器
                const downloadBtn = document.getElementById('download-btn');
                const originalText = downloadBtn.innerHTML;
                downloadBtn.innerHTML = '<i class="fa-solid fa-spinner fa-spin"></i> Generating PDF...';
                downloadBtn.disabled = true;
                
                // 创建表单并提交到后端
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = '{% url "audio_upload:download_report_pdf" %}';
                
                // 添加CSRF令牌
                const csrfToken = '{{ csrf_token }}';
                const csrfInput = document.createElement('input');
                csrfInput.type = 'hidden';
                csrfInput.name = 'csrfmiddlewaretoken';
                csrfInput.value = csrfToken;
                form.appendChild(csrfInput);
                
                // 添加分析数据
                const analysisDataInput = document.createElement('input');
                analysisDataInput.type = 'hidden';
                analysisDataInput.name = 'analysis_data';
                analysisDataInput.value = analysisDetailsString;
                form.appendChild(analysisDataInput);
                
                // 添加到文档并提交
                document.body.appendChild(form);
                form.submit();
                
                // 恢复按钮状态（延迟执行，因为页面会重定向）
                setTimeout(() => {
                    downloadBtn.innerHTML = originalText;
                    downloadBtn.disabled = false;
                }, 3000);
            });
        });

        // Back button functionality removed
    </script>
</body>
</html>