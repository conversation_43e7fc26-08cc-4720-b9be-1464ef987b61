from django.http import JsonResponse
from django.shortcuts import render, redirect
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import login_required, user_passes_test
from django.core.paginator import Paginator
from django.db.models import Q

from django_ratelimit.decorators import ratelimit

from .models import Inquiry
from .forms import InquiryForm
from project.settings import API_BASE_URL, LOCAL_BASE_URL


@require_http_methods(['GET', 'POST'])
def inquiry_view(request):

    if request.method == 'GET':
        context = {
            'API_BASE_URL': API_BASE_URL,
            'LOCAL_BASE_URL': LOCAL_BASE_URL
        }
        return render(request, 'inquiry/inquiry-create.html', context)

    elif request.method == 'POST':

        form = InquiryForm(request.POST)

        if form.is_valid():
            form.save()
            return redirect("contact-success")

        context = {
            'errors': form.errors,
            'data': request.POST,
            'API_BASE_URL': API_BASE_URL,
            'LOCAL_BASE_URL': LOCAL_BASE_URL
        }
        return render(request, 'inquiry/inquiry-create.html', context)
    

@require_http_methods(['POST'])
@ratelimit(key='ip', rate='10/min', method=ratelimit.ALL, block=True)
def inquiry_api_view(request):
    """
    Frontend inquiry API view that forwards requests to backend API
    """
    import requests

    try:
        # Prepare data for backend API
        data = {
            'name': request.POST.get('name', ''),
            'email': request.POST.get('email', ''),
            'inquiry_type': request.POST.get('inquiry_type', '0'),
            'description': request.POST.get('description', '')
        }

        # Call backend API
        backend_url = f"{API_BASE_URL}/api/contact/inquiry/"
        response = requests.post(backend_url, data=data, timeout=30)

        if response.status_code == 201:
            # Success
            backend_data = response.json()
            return JsonResponse({
                'success': True,
                'message': backend_data.get('message', 'Inquiry submitted successfully')
            }, status=200)
        else:
            # Error from backend
            try:
                error_data = response.json()
                return JsonResponse({
                    'success': False,
                    'error': error_data.get('error', 'Failed to submit inquiry')
                }, status=400)
            except:
                return JsonResponse({
                    'success': False,
                    'error': 'Failed to submit inquiry'
                }, status=400)

    except requests.exceptions.Timeout:
        return JsonResponse({
            'success': False,
            'error': 'Request timeout. Please try again.'
        }, status=500)
    except requests.exceptions.ConnectionError:
        return JsonResponse({
            'success': False,
            'error': 'Unable to connect to server. Please try again later.'
        }, status=500)
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': 'An unexpected error occurred. Please try again.'
        }, status=500)


@require_http_methods(['GET'])
def inquiry_success(request):

    return render(request, "components/pages/success.html", {
        'title': 'Inquiry Submitted',
        'description': 'Thank you for taking time to submit an inquiry, our team will be in touch shortly.'
    })


def is_admin_user(user):
    """Check if user is admin (staff or in Data Analysts group)"""
    return user.is_authenticated and (user.is_staff or user.groups.filter(name='Data Analysts').exists())


@login_required
@user_passes_test(is_admin_user)
def message_list_view(request):
    """Admin view to list all messages from backend API"""
    import requests

    # Get search and filter parameters
    search_query = request.GET.get('search', '')
    inquiry_type = request.GET.get('type', '')
    page_number = request.GET.get('page', 1)

    try:
        # Get user's JWT token for backend API call
        # Note: In a proper implementation, you'd get this from the user's session
        # For now, we'll create a simple message list without backend API
        # since the frontend admin doesn't have JWT tokens

        # Fallback: Create empty context for now
        # TODO: Implement proper JWT token handling for admin users
        context = {
            'page_obj': None,
            'search_query': search_query,
            'inquiry_type': inquiry_type,
            'inquiry_choices': [
                (0, 'General Inquiry'),
                (1, 'Technical Support'),
                (2, 'Partnership'),
                (3, 'Media Inquiry'),
                (4, 'Other'),
            ],
            'total_messages': 0,
            'API_BASE_URL': API_BASE_URL,
            'LOCAL_BASE_URL': LOCAL_BASE_URL,
            'error_message': 'Message management is currently being migrated to the backend API. Please check back later.'
        }

        return render(request, 'inquiry/message-list.html', context)

    except Exception as e:
        context = {
            'page_obj': None,
            'search_query': search_query,
            'inquiry_type': inquiry_type,
            'inquiry_choices': [
                (0, 'General Inquiry'),
                (1, 'Technical Support'),
                (2, 'Partnership'),
                (3, 'Media Inquiry'),
                (4, 'Other'),
            ],
            'total_messages': 0,
            'API_BASE_URL': API_BASE_URL,
            'LOCAL_BASE_URL': LOCAL_BASE_URL,
            'error_message': 'Unable to load messages at this time.'
        }

        return render(request, 'inquiry/message-list.html', context)