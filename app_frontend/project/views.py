from django.http import HttpResponseForbidden
from django.shortcuts import render, HttpResponse
from project.settings import LOCAL_BASE_URL, API_BASE_URL

from django_ratelimit.exceptions import Ratelimited

def rate_limiter_view(request, *args, **kwargs):
    return render(request, 'ratelimit.html', status=429)

def view_404(request, *args, **kwargs):
    return render(request, '404.html', status=404)

def handler_403(request, exception=None):
    if isinstance(exception, Ratelimited):
        return HttpResponse('Sorry too many requests, please wait', status=429)
    return HttpResponseForbidden('Forbidden')

def home_view(request):
    from project.settings import API_BASE_URL, LOCAL_BASE_URL
    return render(request, 'html/home.html', {
        'API_BASE_URL': API_BASE_URL,
        'LOCAL_BASE_URL': LOCAL_BASE_URL
    }, status=200)

def about_view(request):
    from project.settings import API_BASE_URL, LOCAL_BASE_URL
    return render(request, 'html/about.html', {
        'API_BASE_URL': API_BASE_URL,
        'LOCAL_BASE_URL': LOCAL_BASE_URL
    }, status=200)

def blogs_view(request):
    from project.settings import API_BASE_URL, LOCAL_BASE_URL
    return render(request, 'html/blogs.html', {
        'API_BASE_URL': API_BASE_URL,
        'LOCAL_BASE_URL': LOCAL_BASE_URL
    }, status=200)


def auth_test_view(request):
    from project.settings import API_BASE_URL, LOCAL_BASE_URL
    return render(request, 'html/auth_test.html', {
        'API_BASE_URL': API_BASE_URL,
        'LOCAL_BASE_URL': LOCAL_BASE_URL
    }, status=200)

def register_page(request):
    return render(request, 'html/register.html', {'LOCAL_BASE_URL': LOCAL_BASE_URL, 'API_BASE_URL': API_BASE_URL})

def login_page(request):
    # Get the next parameter for redirect after login
    next_url = request.GET.get('next', '')
    context = {
        'API_BASE_URL': API_BASE_URL,
        'next_url': next_url
    }
    return render(request, 'html/login.html', context)

def verify_code_page(request):
    return render(request, 'html/verify-code.html', {'API_BASE_URL': API_BASE_URL, 'LOCAL_BASE_URL': LOCAL_BASE_URL})

def password_reset_page(request):
    return render(request, 'html/password_reset.html', {'API_BASE_URL': API_BASE_URL, 'LOCAL_BASE_URL': LOCAL_BASE_URL})

def password_reset_confirm_page(request):
    return render(request, 'html/password_reset_confirm.html', {'API_BASE_URL': API_BASE_URL, 'LOCAL_BASE_URL': LOCAL_BASE_URL})

def profile_page(request):
    return render(request, 'html/profile.html', {'API_BASE_URL': API_BASE_URL, 'LOCAL_BASE_URL': LOCAL_BASE_URL})

def settings_page(request):
    return render(request, 'html/settings.html', {'API_BASE_URL': API_BASE_URL, 'LOCAL_BASE_URL': LOCAL_BASE_URL})

def audio_history_page(request):
    return render(request, 'html/audio_history.html', {'API_BASE_URL': API_BASE_URL, 'LOCAL_BASE_URL': LOCAL_BASE_URL})

def audio_detail_page(request, analysis_id):
    return render(request, 'html/audio_detail.html', {'API_BASE_URL': API_BASE_URL, 'LOCAL_BASE_URL': LOCAL_BASE_URL})

def test_new_features_page(request):
    return render(request, 'html/test_new_features.html', {'API_BASE_URL': API_BASE_URL, 'LOCAL_BASE_URL': LOCAL_BASE_URL})

def dashboard_page(request):
    return render(request, 'html/dashboard.html', {'API_BASE_URL': API_BASE_URL, 'LOCAL_BASE_URL': LOCAL_BASE_URL})