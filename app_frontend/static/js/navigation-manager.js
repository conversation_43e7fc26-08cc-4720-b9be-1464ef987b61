/**
 * Navigation Manager - Handles smart navigation with login flow
 * Ensures back button skips login page and returns to original page
 */

class NavigationManager {
    constructor() {
        this.init();
    }

    init() {
        // Store original page when navigation starts
        this.storeOriginalPage();
        
        // Handle browser back/forward buttons
        window.addEventListener('popstate', (event) => {
            this.handlePopState(event);
        });
    }

    /**
     * Store the current page as the original page before navigation
     */
    storeOriginalPage() {
        if (!sessionStorage.getItem('originalPage')) {
            sessionStorage.setItem('originalPage', window.location.href);
        }
    }

    /**
     * Check if user is authenticated
     */
    isAuthenticated() {
        const token = localStorage.getItem('access_token');
        return token && token !== 'null' && token !== '';
    }

    /**
     * Navigate to a page with login check
     * @param {string} targetUrl - The URL to navigate to
     * @param {boolean} requiresAuth - Whether the page requires authentication
     * @param {string} buttonId - ID of the button that triggered navigation (optional)
     */
    navigateWithAuthCheck(targetUrl, requiresAuth = false, buttonId = null) {
        console.log('🔍 Navigation requested:', { targetUrl, requiresAuth, buttonId });

        if (requiresAuth && !this.isAuthenticated()) {
            console.log('🔒 Authentication required, redirecting to login');

            // Clear any existing stored URLs to prevent conflicts
            sessionStorage.removeItem('redirectAfterLogin');
            sessionStorage.removeItem('originalPageBeforeLogin');

            // Store the target URL for after login
            sessionStorage.setItem('redirectAfterLogin', targetUrl);

            // Store the original page (where user came from)
            sessionStorage.setItem('originalPageBeforeLogin', window.location.href);

            console.log('📝 Stored navigation context:', {
                redirectAfterLogin: targetUrl,
                originalPageBeforeLogin: window.location.href
            });

            // Navigate to login page
            window.location.href = '/login/';
        } else {
            console.log('✅ Navigation allowed, going to:', targetUrl);

            // Clear any stored redirect URLs since we're navigating directly
            sessionStorage.removeItem('redirectAfterLogin');
            sessionStorage.removeItem('originalPageBeforeLogin');

            // Navigate to target page
            window.location.href = targetUrl;
        }
    }

    /**
     * Handle successful login - redirect to intended page and manipulate history
     */
    handleLoginSuccess() {
        const redirectUrl = sessionStorage.getItem('redirectAfterLogin');
        const originalPage = sessionStorage.getItem('originalPageBeforeLogin');

        console.log('✅ Login successful, handling redirect:', { redirectUrl, originalPage });

        if (redirectUrl && originalPage) {
            // Check if we're redirecting to a different page than where user came from
            const redirectPath = new URL(redirectUrl, window.location.origin).pathname;
            const originalPath = new URL(originalPage, window.location.origin).pathname;

            if (redirectPath !== originalPath) {
                console.log('🔄 Different page redirect detected, using special history handling');

                // For different page redirects (like Contact Us case),
                // we need to set up the history so back button goes to original page

                // Clear the redirect URL
                sessionStorage.removeItem('redirectAfterLogin');

                // Store a flag to indicate we need special history handling
                sessionStorage.setItem('needsHistoryFix', 'true');

                // Navigate to redirect URL
                window.location.replace(redirectUrl);
            } else {
                console.log('🔄 Same page redirect, using normal handling');

                // Clear both values for same-page redirects
                sessionStorage.removeItem('redirectAfterLogin');
                sessionStorage.removeItem('originalPageBeforeLogin');

                // Navigate normally
                window.location.replace(redirectUrl);
            }
        } else if (redirectUrl) {
            // Clear the redirect URL
            sessionStorage.removeItem('redirectAfterLogin');

            // Navigate to redirect URL
            window.location.replace(redirectUrl);
        } else {
            // No specific redirect, go to home page
            window.location.replace('/');
        }
    }

    /**
     * Smart back navigation - skips login page
     */
    smartBack() {
        const originalPage = sessionStorage.getItem('originalPageBeforeLogin');
        const currentUrl = window.location.href;

        console.log('🔙 Smart back requested:', { originalPage, currentUrl });

        // If we have an original page stored and we're not on the login page
        if (originalPage && !currentUrl.includes('/login/')) {
            console.log('🔙 Returning to original page:', originalPage);

            // Clear the stored original page
            sessionStorage.removeItem('originalPageBeforeLogin');

            // Navigate to original page
            window.location.href = originalPage;
        } else {
            // Fallback to normal browser back or home page
            if (window.history.length > 1 && document.referrer) {
                console.log('🔙 Using browser back');
                window.history.back();
            } else {
                console.log('🔙 Fallback to home page');
                window.location.href = '/';
            }
        }
    }

    /**
     * Setup history manipulation after successful login redirect
     * This should be called on the target page after login
     */
    setupPostLoginHistory() {
        const originalPage = sessionStorage.getItem('originalPageBeforeLogin');
        const currentUrl = window.location.href;

        console.log('🔧 Setting up post-login history:', { originalPage, currentUrl });

        if (originalPage) {
            console.log('🔧 Manipulating browser history for proper back navigation');

            // Replace the current history entry with state that includes original page info
            // This allows the back button to skip the login page and go directly to original page
            history.replaceState(
                {
                    originalPage: originalPage,
                    skipLogin: true,
                    fromLogin: true
                },
                document.title,
                currentUrl
            );

            // Add popstate listener for back button handling
            const handlePopState = (event) => {
                console.log('🔙 Back button clicked, event state:', event.state);

                if (event.state && event.state.fromLogin && event.state.originalPage) {
                    console.log('🔙 Detected back navigation after login, going to original page:', event.state.originalPage);

                    // Remove the listener to prevent multiple triggers
                    window.removeEventListener('popstate', handlePopState);

                    // Navigate to original page
                    window.location.href = event.state.originalPage;
                    return;
                }
            };

            // Add the popstate listener
            window.addEventListener('popstate', handlePopState);

            // Clear the stored original page
            sessionStorage.removeItem('originalPageBeforeLogin');
            console.log('🧹 Cleared originalPageBeforeLogin from sessionStorage');
            console.log('✅ Post-login history setup complete');
        } else {
            console.log('🔧 No original page stored, no history manipulation needed');
        }
    }

    /**
     * Handle browser back/forward button
     */
    handlePopState(event) {
        // This handles browser back/forward buttons
        // You can add custom logic here if needed
        console.log('🔙 Browser navigation detected');
    }

    /**
     * Clear all stored navigation data
     */
    clearNavigationData() {
        sessionStorage.removeItem('redirectAfterLogin');
        sessionStorage.removeItem('originalPageBeforeLogin');
        sessionStorage.removeItem('originalPage');
    }

    /**
     * Initialize navigation for buttons that require authentication
     * @param {string} selector - CSS selector for buttons
     * @param {string} targetUrl - URL to navigate to
     */
    initAuthButton(selector, targetUrl) {
        const buttons = document.querySelectorAll(selector);
        buttons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                this.navigateWithAuthCheck(targetUrl, true, button.id);
            });
        });
    }

    /**
     * Initialize smart back buttons
     * @param {string} selector - CSS selector for back buttons
     */
    initSmartBackButtons(selector) {
        const buttons = document.querySelectorAll(selector);
        buttons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                this.smartBack();
            });
        });
    }
}

// Create global instance
window.navigationManager = new NavigationManager();

// Expose methods globally for easy access
window.navigateWithAuth = (targetUrl, requiresAuth = false) => {
    window.navigationManager.navigateWithAuthCheck(targetUrl, requiresAuth);
};

window.smartBack = () => {
    window.navigationManager.smartBack();
};

window.handleLoginSuccess = () => {
    window.navigationManager.handleLoginSuccess();
};

window.setupPostLoginHistory = () => {
    window.navigationManager.setupPostLoginHistory();
};

// Global function to handle any button click that might require authentication
window.handleAuthenticatedAction = (targetUrl, requiresAuth = false, actionName = 'navigation') => {
    console.log(`🔘 ${actionName} requested:`, { targetUrl, requiresAuth });

    if (requiresAuth && !window.navigationManager.isAuthenticated()) {
        console.log(`🔒 Authentication required for ${actionName}`);

        // Clear any existing stored URLs
        sessionStorage.removeItem('redirectAfterLogin');
        sessionStorage.removeItem('originalPageBeforeLogin');

        // Store current page and target
        sessionStorage.setItem('redirectAfterLogin', targetUrl);
        sessionStorage.setItem('originalPageBeforeLogin', window.location.href);

        console.log('📝 Stored authentication context:', {
            redirectAfterLogin: targetUrl,
            originalPageBeforeLogin: window.location.href
        });

        // Redirect to login
        window.location.href = '/login/';
        return false; // Prevent default action
    } else {
        console.log(`✅ ${actionName} allowed, proceeding to:`, targetUrl);

        // Clear any stored URLs since we're navigating directly
        sessionStorage.removeItem('redirectAfterLogin');
        sessionStorage.removeItem('originalPageBeforeLogin');

        // Navigate to target
        window.location.href = targetUrl;
        return true; // Allow action to proceed
    }
};

// Global function to check if this page was reached after login
window.checkPostLoginSetup = () => {
    const redirectAfterLogin = sessionStorage.getItem('redirectAfterLogin');
    const originalPage = sessionStorage.getItem('originalPageBeforeLogin');
    const currentUrl = window.location.href;

    console.log('🔍 Checking post-login setup:', {
        redirectAfterLogin,
        originalPage,
        currentUrl
    });

    // If we have these values, it means we just came from a login redirect
    if (redirectAfterLogin && originalPage) {
        // Check if current URL matches the redirect URL (normalize URLs for comparison)
        const currentPath = new URL(currentUrl).pathname;
        const redirectPath = new URL(redirectAfterLogin, window.location.origin).pathname;

        if (currentPath === redirectPath) {
            console.log('✅ Detected post-login page load, setting up history');

            // Clear the redirect URL since we're now on the target page
            sessionStorage.removeItem('redirectAfterLogin');

            // Setup history manipulation
            setTimeout(() => {
                window.navigationManager.setupPostLoginHistory();

                // Refresh authentication status on all pages
                if (window.refreshAuthStatus) {
                    window.refreshAuthStatus();
                }
            }, 100); // Small delay to ensure page is fully loaded
        } else {
            console.log('🔍 URL mismatch, not setting up post-login history');
            console.log('Current path:', currentPath);
            console.log('Expected path:', redirectPath);
        }
    }

    // Always refresh auth status when page loads
    setTimeout(() => {
        if (window.refreshAuthStatus) {
            console.log('🔄 Refreshing auth status on page load');
            window.refreshAuthStatus();
        }
    }, 200);
};

console.log('🚀 Navigation Manager initialized');
