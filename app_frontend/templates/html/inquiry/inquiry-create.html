
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Us - HiSage Health</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Inter', sans-serif;
        }
        .contact-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
            margin: 2rem 0;
        }
        .contact-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 0.75rem 2rem;
            border-radius: 50px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .success-message {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        .error-message {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        .contact-info {
            background: #f8f9fa;
            padding: 2rem;
            border-radius: 10px;
            margin-top: 2rem;
        }
        .readonly-field {
            background-color: #f8f9fa !important;
            cursor: not-allowed;
        }
        .auto-filled-badge {
            font-size: 0.75rem;
            margin-left: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 col-md-10">
                <div class="contact-container">
                    <div class="contact-header">
                        <h1><i class="fas fa-envelope me-3"></i>Contact Us</h1>
                        <p class="mb-0">We'd love to hear from you. Send us a message and we'll respond as soon as possible.</p>
                        <div id="user-status" class="mt-2" style="display: none;">
                            <small><i class="fas fa-user-check me-1"></i>Logged in as: <span id="user-email"></span></small>
                        </div>
                    </div>

                    <div class="p-4">
                        <!-- Success/Error Messages -->
                        <div id="message-container"></div>

                        <!-- Login Status -->
                        <div id="login-prompt" class="alert alert-info" style="display: none;">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Note:</strong> You can contact us without logging in, but if you have an account,
                            <a href="#" class="alert-link" id="login-link">please log in</a> for a better experience.
                        </div>

                        <form id="contact-form">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="name" class="form-label">
                                        <i class="fas fa-user me-2"></i>Full Name
                                        <span id="name-auto-filled" class="text-success" style="display: none;">(Auto-filled)</span>
                                    </label>
                                    <input type="text" class="form-control" id="name" name="name" placeholder="Optional">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">
                                        <i class="fas fa-envelope me-2"></i>Email Address *
                                        <span id="email-auto-filled" class="text-success" style="display: none;">(Auto-filled)</span>
                                    </label>
                                    <input type="email" class="form-control" id="email" name="email" required>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-12 mb-3">
                                    <label for="subject" class="form-label">
                                        <i class="fas fa-tag me-2"></i>Subject *
                                    </label>
                                    <select class="form-control" id="subject" name="inquiry_type" required>
                                        <option value="">Select a subject</option>
                                        <option value="0">General Inquiry</option>
                                        <option value="1">Technical Support</option>
                                        <option value="2">Partnership</option>
                                        <option value="3">Media Inquiry</option>
                                        <option value="4">Other</option>
                                    </select>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="message" class="form-label">
                                    <i class="fas fa-comment me-2"></i>Message *
                                </label>
                                <textarea class="form-control" id="message" name="description" rows="6" required
                                    placeholder="Please describe your inquiry in detail..."></textarea>
                                <div class="form-text">Maximum 1500 characters</div>
                            </div>

                            <div class="text-center">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-paper-plane me-2"></i>Send Message
                                </button>
                            </div>
                        </form>

                        <div class="contact-info">
                            <div class="row text-center">
                                <div class="col-md-4 mb-3">
                                    <i class="fas fa-map-marker-alt fa-2x text-primary mb-2"></i>
                                    <h6>Address</h6>
                                    <p class="text-muted">HiSage Health<br>Innovation Center</p>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <i class="fas fa-envelope fa-2x text-primary mb-2"></i>
                                    <h6>Email</h6>
                                    <p class="text-muted"><EMAIL></p>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <i class="fas fa-clock fa-2x text-primary mb-2"></i>
                                    <h6>Response Time</h6>
                                    <p class="text-muted">Within 48 hours</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Navigation Manager for handling login redirects -->
    <script src="{{ LOCAL_BASE_URL }}/static/js/navigation-manager.js"></script>

    <script>
        // Configuration
        const API_BASE_URL = '{{ API_BASE_URL }}';
        const LOCAL_BASE_URL = '{{ LOCAL_BASE_URL }}';

        document.getElementById('contact-form').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const messageContainer = document.getElementById('message-container');

            // Show loading state
            const submitButton = this.querySelector('button[type="submit"]');
            const originalText = submitButton.innerHTML;
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Sending...';
            submitButton.disabled = true;

            try {
                const response = await fetch(`${LOCAL_BASE_URL}/contact-us/api/`, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-CSRFToken': getCookie('csrftoken')
                    }
                });

                if (response.ok) {
                    messageContainer.innerHTML = `
                        <div class="success-message">
                            <i class="fas fa-check-circle me-2"></i>
                            <strong>Message sent successfully!</strong> We'll get back to you within 48 hours.
                        </div>
                    `;
                    // Only reset non-readonly fields
                    const nonReadonlyFields = this.querySelectorAll('input:not([readonly]), textarea, select');
                    nonReadonlyFields.forEach(field => {
                        if (field.type !== 'submit' && field.id !== 'name' && field.id !== 'email') {
                            field.value = '';
                        }
                    });

                    // Reset subject and message fields specifically
                    document.getElementById('subject').value = '';
                    document.getElementById('message').value = '';
                } else {
                    throw new Error('Failed to send message');
                }
            } catch (error) {
                messageContainer.innerHTML = `
                    <div class="error-message">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Error:</strong> Failed to send message. Please try again.
                    </div>
                `;
            } finally {
                // Restore button state
                submitButton.innerHTML = originalText;
                submitButton.disabled = false;
            }

            // Scroll to message
            messageContainer.scrollIntoView({ behavior: 'smooth' });
        });

        // Get CSRF token
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }

        // Check if user is authenticated
        function isAuthenticated() {
            const token = localStorage.getItem('access_token');
            const isAuth = token && token !== 'null' && token !== '';
            console.log('🔍 Authentication check:', {
                hasToken: !!token,
                tokenLength: token ? token.length : 0,
                isAuthenticated: isAuth
            });
            return isAuth;
        }

        // Get user profile from API
        async function getUserProfile() {
            if (!isAuthenticated()) {
                console.log('🔍 User not authenticated - no token found');
                return null;
            }

            try {
                const token = localStorage.getItem('access_token');
                console.log('🔍 Making API call to get user profile...');

                const response = await fetch(`${API_BASE_URL}/api/user/profile/`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                console.log('🔍 API response status:', response.status);

                if (response.ok) {
                    const data = await response.json();
                    console.log('🔍 API response data:', data);

                    if (data.success && data.user) {
                        // Transform the data to match expected format
                        const userData = {
                            email: data.user.email,
                            name: data.user.first_name && data.user.last_name
                                ? `${data.user.first_name} ${data.user.last_name}`.trim()
                                : data.user.first_name || data.user.last_name || null,
                            first_name: data.user.first_name,
                            last_name: data.user.last_name
                        };
                        console.log('✅ User profile loaded:', userData);
                        return userData;
                    } else {
                        console.log('❌ API response indicates failure or missing user data');
                        return null;
                    }
                } else if (response.status === 401) {
                    console.log('❌ Token expired (401), clearing storage');
                    // Token expired, clear storage
                    localStorage.removeItem('access_token');
                    localStorage.removeItem('refresh_token');
                    localStorage.removeItem('user_info');
                    return null;
                } else {
                    console.log('❌ API call failed with status:', response.status);
                    // Try fallback to stored user info
                    return getFallbackUserInfo();
                }
            } catch (error) {
                console.error('❌ Error fetching user profile:', error);
                // Try fallback to stored user info
                return getFallbackUserInfo();
            }
        }

        // Fallback function to get user info from localStorage
        function getFallbackUserInfo() {
            try {
                const storedUserInfo = localStorage.getItem('user_info');
                if (storedUserInfo) {
                    const userInfo = JSON.parse(storedUserInfo);
                    console.log('🔄 Using fallback user info from localStorage:', userInfo);

                    const userData = {
                        email: userInfo.email,
                        name: userInfo.first_name && userInfo.last_name
                            ? `${userInfo.first_name} ${userInfo.last_name}`.trim()
                            : userInfo.first_name || userInfo.last_name || null,
                        first_name: userInfo.first_name,
                        last_name: userInfo.last_name
                    };
                    return userData;
                }
            } catch (error) {
                console.error('❌ Error parsing stored user info:', error);
            }
            return null;
        }



        // Update UI based on user authentication status
        async function updateUIForUser() {
            console.log('🔍 Starting UI update...');
            const user = await getUserProfile();

            if (user) {
                console.log('✅ User authenticated, updating UI for logged-in state');
                // User is authenticated
                document.getElementById('login-prompt').style.display = 'none';
                document.getElementById('user-status').style.display = 'block';
                document.getElementById('user-email').textContent = user.email;

                // Pre-fill form fields
                const nameField = document.getElementById('name');
                const emailField = document.getElementById('email');

                if (user.name) {
                    nameField.value = user.name;
                } else {
                    nameField.value = 'Anonymous User';
                }
                nameField.readOnly = true;
                nameField.style.backgroundColor = '#f8f9fa';
                nameField.title = 'This field is automatically filled from your account information';
                document.getElementById('name-auto-filled').style.display = 'inline';

                emailField.value = user.email;
                emailField.readOnly = true;
                emailField.style.backgroundColor = '#f8f9fa';
                emailField.title = 'This field is automatically filled from your account information';
                document.getElementById('email-auto-filled').style.display = 'inline';

            } else {
                console.log('❌ User not authenticated, updating UI for logged-out state');
                // User is not authenticated
                document.getElementById('login-prompt').style.display = 'block';
                document.getElementById('user-status').style.display = 'none';

                // Reset form fields to editable state
                const nameField = document.getElementById('name');
                const emailField = document.getElementById('email');

                nameField.value = '';
                nameField.readOnly = false;
                nameField.style.backgroundColor = '';
                nameField.title = '';
                nameField.placeholder = 'Optional';
                document.getElementById('name-auto-filled').style.display = 'none';

                emailField.value = '';
                emailField.readOnly = false;
                emailField.style.backgroundColor = '';
                emailField.title = '';
                document.getElementById('email-auto-filled').style.display = 'none';
            }
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Page loaded, initializing contact form...');
            console.log('🔍 API_BASE_URL:', API_BASE_URL);
            console.log('🔍 LOCAL_BASE_URL:', LOCAL_BASE_URL);

            // Debug localStorage contents
            console.log('🔍 LocalStorage contents:');
            console.log('  - access_token:', localStorage.getItem('access_token') ? 'EXISTS' : 'MISSING');
            console.log('  - refresh_token:', localStorage.getItem('refresh_token') ? 'EXISTS' : 'MISSING');
            console.log('  - user_info:', localStorage.getItem('user_info') ? 'EXISTS' : 'MISSING');

            // Handle post-login history setup to fix back button navigation
            if (window.setupPostLoginHistory) {
                console.log('🔧 Setting up post-login history for proper back navigation...');
                window.setupPostLoginHistory();
            }

            // Check user authentication status and update UI
            updateUIForUser();

            // Handle login link click using smart navigation
            const loginLink = document.getElementById('login-link');
            if (loginLink) {
                loginLink.addEventListener('click', function(e) {
                    e.preventDefault();

                    console.log('🔗 Contact Us login link clicked');

                    // Clear any existing stored URLs to prevent conflicts
                    sessionStorage.removeItem('redirectAfterLogin');
                    sessionStorage.removeItem('originalPageBeforeLogin');

                    const currentUrl = window.location.href;
                    const referrer = document.referrer;

                    console.log('🔍 Current URL:', currentUrl);
                    console.log('🔍 Referrer:', referrer);

                    // Store current Contact Us page as redirect target (return here after login)
                    sessionStorage.setItem('redirectAfterLogin', currentUrl);

                    // Store the referrer as the original page (for back button navigation)
                    if (referrer && !referrer.includes('/login/') && !referrer.includes('/contact-us/')) {
                        sessionStorage.setItem('originalPageBeforeLogin', referrer);
                        console.log('📝 Stored referrer as original page:', referrer);
                    } else {
                        // Fallback to home page if no valid referrer
                        sessionStorage.setItem('originalPageBeforeLogin', '/');
                        console.log('📝 No valid referrer, using home page as original');
                    }

                    console.log('📝 Login context stored:', {
                        redirectAfterLogin: currentUrl,
                        originalPageBeforeLogin: sessionStorage.getItem('originalPageBeforeLogin')
                    });

                    // Navigate to login page
                    window.location.href = '/login/';
                });
            }
        });

        // Manual test function for debugging (can be called from browser console)
        window.testUserAPI = async function() {
            console.log('🧪 Manual API test started...');
            const token = localStorage.getItem('access_token');
            console.log('🧪 Token exists:', !!token);
            console.log('🧪 Token preview:', token ? token.substring(0, 20) + '...' : 'null');

            if (!token) {
                console.log('🧪 No token found, cannot test API');
                return;
            }

            try {
                const response = await fetch(`${API_BASE_URL}/api/user/profile/`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                console.log('🧪 Response status:', response.status);
                console.log('🧪 Response headers:', [...response.headers.entries()]);

                const data = await response.json();
                console.log('🧪 Response data:', data);

                return data;
            } catch (error) {
                console.error('🧪 API test error:', error);
                return null;
            }
        };

        // Check for authentication status changes (e.g., after returning from login)
        window.addEventListener('focus', function() {
            console.log('🔍 Window gained focus, re-checking auth status...');
            // Re-check authentication status when window gains focus
            updateUIForUser();
        });

        // Also check when page becomes visible (for mobile browsers)
        document.addEventListener('visibilitychange', function() {
            if (!document.hidden) {
                console.log('🔍 Page became visible, re-checking auth status...');
                updateUIForUser();
            }
        });
    </script>
</body>
</html>

