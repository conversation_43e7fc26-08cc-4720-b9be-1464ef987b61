# Generated migration for Inquiry model

from django.db import migrations, models
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('api', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Inquiry',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(blank=True, max_length=200, null=True, verbose_name='Full Name')),
                ('email', models.EmailField(max_length=254, verbose_name='Email Address')),
                ('inquiry_type', models.PositiveSmallIntegerField(choices=[(0, 'General Inquiry'), (1, 'Technical Support'), (2, 'Partnership'), (3, 'Media Inquiry'), (4, 'Other')], default=0, verbose_name='Inquiry Type')),
                ('description', models.TextField(max_length=1500, verbose_name='Message')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
            ],
            options={
                'verbose_name': 'Contact Inquiry',
                'verbose_name_plural': 'Contact Inquiries',
                'ordering': ['-created_at'],
            },
        ),
    ]
