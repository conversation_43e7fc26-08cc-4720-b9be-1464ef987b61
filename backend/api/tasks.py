import json
from celery import shared_task
from mmse_prediction.pipeline_functions.pipeline import mmse_predict_pipeline
from mmse_prediction.pipeline_functions.other_functions import NpEncoder
from celery.signals import worker_process_init
pipe = None

@worker_process_init.connect
def init_pipe(**kwargs):
    global pipe
    pipe = mmse_predict_pipeline()

@shared_task
def mmse_predict(audioanalysis_id, file_path):
    print(f"🚀 开始MMSE预测任务:")
    print(f"   任务ID: {audioanalysis_id}")
    print(f"   音频路径: {file_path}")

    # 更新状态为处理中
    from .models import AudioAnalysis
    try:
        instance = AudioAnalysis.objects.get(id=audioanalysis_id)
        instance.status = "processing"
        instance.save()
        print(f"✅ 状态已更新为处理中")
    except Exception as e:
        print(f"❌ 更新状态失败: {str(e)}")
        return

    global pipe
    print(f"⏳ 开始MMSE预测分析...")

    dict_ = pipe.predict(file_path)
    print(f"📊 MMSE预测完成")

    # dict_ = {}
    # dict_['Predicted mmse score'] = 27.0
    # dict_['Model performance'] = {'RMSE': 3.79, 'Pearson correlation coefficient': 0.83}
    # dict_['Transcribed'] = 'test Transcribed'
    # dict_['Model'] = 'HiSage-MMScore-en-1.0'

    # Write back to database
    from .models import AudioAnalysis
    try:
        instance = AudioAnalysis.objects.get(id=audioanalysis_id)
        instance.result = json.dumps(dict_, cls=NpEncoder)
        instance.status = "completed"
        instance.save()
        print(f"✅ 音频分析完成: {audioanalysis_id}")
        print(f"📋 分析结果已保存到数据库")
    except Exception as e:
        print(f"❌ 音频分析失败: {audioanalysis_id}, 错误: {str(e)}")
        try:
            instance = AudioAnalysis.objects.get(id=audioanalysis_id)
            instance.status = "failed"
            instance.save()
            print(f"💾 状态已更新为失败")
        except Exception as save_error:
            print(f"❌ 保存失败状态时出错: {str(save_error)}")